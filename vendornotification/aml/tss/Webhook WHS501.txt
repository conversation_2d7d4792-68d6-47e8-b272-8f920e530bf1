TrackWizz API Document
Webhook to capture initial screening

case details and send to Ex ernal
SourceSystem (WHS501) Document

Document version: v1.0

1

Confidential



I. Purpose: This Webhook will provide the below list
Gives details of the webhook for initial screening which will be used for capturing the case details and

sending it to the Source System.

II. Logic

● Introduce the Onboarding Decision button for Initial Screening cases under the Action section.

● This decision will be stored for the case and will be used to send to the external source system

defined in Webhook Master

● The trigger to the external source will happen when the user clicks on the Close With No Action

and Close With Action buttons

● If the external system is down, Screenzaa will close the case and intimate the user via an email

that the external system is down

● The system will retry 3 times to contact the external system in the exponential mechanism

III. Request Details

Request - Header

Field Name Mandatory Data Type Remarks

APIToken No String Static Value will be shared by the client which will be
passed here for every webhook call

ContentType Yes String Application/JSON

Signature Yes String Authorization of end source system

Request - Body
Field Name Mandatory Data Type Remarks

RequestData Yes Complex Refer 1.1. Case Details will be passed. One case at a time

EventType Yes String WHS501

2

Confidential



Table 1.1 Request - Data

Field Name Mandatory Data Type Remarks

RequestID Yes String A unique serial number is used to identify the record
for processing. RequestId should be different for every
record

Case ID Yes String Case ID

SourcesystemCustomerCo
de Yes String SourceSystemCustomerCode of the Customer

ApplicationRefNumber No String Application ref number of the Customer

OnboardingDecision Yes Enum The value will be sent only in case of Initial Screening
case.
Possible values: Proceed/Decline

NoMatchCount Yes String Alert count where No match is marked

TrueMatchCount Yes String Alert count where Truematch is marked

CaseStage Yes String Current Stage of the case

CaseClosedBy Yes String A person who has closed the case

CaseClosureDate No String The date on which the case was closed

FinalRemarks No String Final remarks on the case

CaseActions Mrugank on 21-Dec-2023: Reviewed

Array(compl Susheel on 23-Dec-2023: Checked
No ex object) Refer Table 1.2

3

Confidential



Table 1.2 Case Actions
Field Name Mandatory Data Type Remarks

Name Yes String Assignee

Date Yes String The date on which he has put actions

Action Yes String Actions

Note:
On a successful response, HTTP code 200 needs to be sent in response

Validating the webhook URL

- On the client side, the webhook URL should contain a post endpoint which will verify the
signature and return the status as 200
In the POST request for validation the request body will contain an event of type
ValidateEndPoint which means on client side the signature needs to be verified (optional ) and
the response code 200 is to be sent on successful verification.

{
"EventType": "ValidateEndPoint"

}

Webhook Endpoint Verification ( OPTIONAL)

- In the request Headers, we pass the Signature header which is an HMAC SHA256 hash of the
Request Body data generated using Endpoint Secret as a key.

- On the client side, this needs to be verified by generating the same hash and comparing it with
the provided Signature (hash) in the request header.

Sample code for verifying hash :

4

Confidential



Request Headers

content-type application/json; charset=utf-8

signature g+7GCgw/6480ru5P6xOo59f80pnCF85y8Hxk3ZCkTwY=

Please find below the attached Request Body when Action is taken on a Case

Request Body

{
"EventType": "WHS501",
"RequestData": {
"RequestId": "12345",
"CaseId": "364736",
"SourceSystemCustomerCode": "1234",
"ApplicationRefNumber": "APP4567",
"OnboardingDecision": "Proceed",
"NoMatchCount": "5",
"TrueMatchCount": "0",
"CaseStage": "Level 2",
"CaseClosedBy": "susheel.nair",
"CaseClosureDate": "07-Jun-2024",
"FinalRemarks": "",
"CaseActions": [
{
"Name": "susheell.nair",
"Date": "07-Jun-2024",
"Action": "jkk"

}
]

}
}

5

Confidential