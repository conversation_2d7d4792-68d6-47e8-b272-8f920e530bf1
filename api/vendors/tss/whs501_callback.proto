syntax = "proto3";

package vendors.tss;

option go_package = "github.com/epifi/gamma/api/vendors/tss";
option java_package = "com.github.epifi.gamma.api.vendors.tss";

// WHS501 Webhook Messages
message ProcessWHS501CallbackRequest {
  string event_type = 1 [json_name = "EventType"];
  WHS501RequestData request_data = 2 [json_name = "RequestData"];
}

message WHS501RequestData {
  string request_id = 1 [json_name = "RequestId"];
  string case_id = 2 [json_name = "CaseId"];
  string source_system_customer_code = 3 [json_name = "SourceSystemCustomerCode"];
  string application_ref_number = 4 [json_name = "ApplicationRefNumber"];
  string onboarding_decision = 5 [json_name = "OnboardingDecision"];
  string no_match_count = 6 [json_name = "NoMatchCount"];
  string true_match_count = 7 [json_name = "TrueMatchCount"];
  string case_stage = 8 [json_name = "CaseStage"];
  string case_closed_by = 9 [json_name = "CaseClosedBy"];
  string case_closure_date = 10 [json_name = "CaseClosureDate"];
  string final_remarks = 11 [json_name = "FinalRemarks"];
  repeated WHS501CaseAction case_actions = 12 [json_name = "CaseActions"];
}

message WHS501CaseAction {
  string name = 1 [json_name = "Name"];
  string date = 2 [json_name = "Date"];
  string action = 3 [json_name = "Action"];
}

message ProcessWHS501CallbackResponse {
  string status = 1 [json_name = "status"];
}
